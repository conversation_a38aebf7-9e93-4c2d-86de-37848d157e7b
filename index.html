<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON><PERSON> ký / <PERSON><PERSON><PERSON> nh<PERSON>p</title>
    <link rel="stylesheet" href="style.css" />
  </head>
  <body>
    <div class="container">
      <!-- Form đăng nhập -->
      <div class="form-container" id="loginForm">
        <h2>Login</h2>
        <form id="loginFormElement">
          <div class="input-group">
            <label for="loginEmail">Email</label>
            <input type="email" id="loginEmail" required placeholder="Email" />
          </div>
          <div class="input-group">
            <label for="loginPassword">Password</label>
            <input
              type="password"
              id="loginPassword"
              required
              placeholder="Password"
            />
          </div>
          <p style="text-align: right; margin-bottom: 15px">
            <a
              href="#"
              style="color: #4f46e5; text-decoration: none; font-size: 14px"
              >Forgot password?</a
            >
          </p>
          <button type="submit" class="btn-primary">Login</button>
          <p class="switch-form" style="margin: 15px 0">
            Don't have an account? <a href="#" id="showRegister">Sign up</a>
          </p>
          <div class="divider"><span>Or</span></div>
          <button type="button" class="btn-google" id="googleLoginBtn">
            <img
              src="https://developers.google.com/identity/images/g-logo.png"
              alt="Google"
              width="20"
            />
            Login with Google
          </button>
        </form>
      </div>

      <!-- Form đăng ký -->
      <div class="form-container hidden" id="registerForm">
        <h2>Signup</h2>
        <form id="registerFormElement">
          <div class="input-group">
            <label for="registerEmail">Email</label>
            <input
              type="email"
              id="registerEmail"
              required
              placeholder="Email"
            />
          </div>
          <div class="input-group">
            <label for="registerPassword">Create password</label>
            <input
              type="password"
              id="registerPassword"
              required
              placeholder="Create password"
            />
          </div>
          <div class="input-group">
            <label for="confirmPassword">Confirm password</label>
            <input
              type="password"
              id="confirmPassword"
              required
              placeholder="Confirm password"
            />
            <small class="password-hint">
              Password must have at least 6 characters, including: 1 uppercase,
              1 lowercase, 1 number
            </small>
          </div>
          <button type="submit" class="btn-primary">Signup</button>
          <p class="switch-form" style="margin: 15px 0">
            Already have an account? <a href="#" id="showLogin">Login</a>
          </p>
          <div class="divider"><span>Or</span></div>
          <button type="button" class="btn-google" id="googleSignupBtn">
            <img
              src="https://developers.google.com/identity/images/g-logo.png"
              alt="Google"
              width="20"
            />
            Login with Google
          </button>
        </form>
      </div>

      <!-- Thông tin người dùng sau khi đăng nhập -->
      <div class="user-info hidden" id="userInfo">
        <h2>Chào mừng!</h2>
        <div class="user-details">
          <p><strong>Email:</strong> <span id="userEmail"></span></p>
          <p><strong>Tên:</strong> <span id="userName"></span></p>
        </div>
        <button class="btn-secondary" id="logoutBtn">Đăng xuất</button>
      </div>

      <!-- Thông báo -->
      <div class="message hidden" id="message">
        <span id="messageText"></span>
      </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>

    <!-- Custom JavaScript -->
    <script src="script.js"></script>
  </body>
</html>
