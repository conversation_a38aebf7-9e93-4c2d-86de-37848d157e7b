<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON><PERSON> ký / <PERSON><PERSON><PERSON> nhập</title>
    <link rel="stylesheet" href="style.css" />
  </head>
  <body>
    <div class="container">
      <!-- Form đăng nhập -->
      <div class="form-container" id="loginForm">
        <h2>Login</h2>
        <form id="loginFormElement">
          <div class="input-group">
            <label for="loginEmail">Email</label>
            <input type="email" id="loginEmail" required placeholder="Email" />
            <span class="error-message" id="loginEmailError"></span>
          </div>
          <div class="input-group">
            <label for="loginPassword">Password</label>
            <input
              type="password"
              id="loginPassword"
              required
              placeholder="Password"
            />
            <span class="error-message" id="loginPasswordError"></span>
          </div>
          <p style="text-align: right; margin-bottom: 15px">
            <a
              href="#"
              style="color: #4f46e5; text-decoration: none; font-size: 14px"
              >Forgot password?</a
            >
          </p>
          <button type="submit" class="btn-primary">Login</button>
          <p class="switch-form" style="margin: 15px 0">
            Don't have an account? <a href="#" id="showRegister">Sign up</a>
          </p>
          <div class="divider"><span>Or</span></div>
          <button type="button" class="btn-facebook" id="facebookLoginBtn">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path
                d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"
              />
            </svg>
            Login with Facebook
          </button>
          <button type="button" class="btn-google" id="googleLoginBtn">
            <img
              src="https://developers.google.com/identity/images/g-logo.png"
              alt="Google"
              width="20"
            />
            Login with Google
          </button>
        </form>
      </div>

      <!-- Form đăng ký -->
      <div class="form-container" id="registerForm">
        <h2>Signup</h2>
        <form id="registerFormElement">
          <div class="input-group">
            <label for="registerEmail">Email</label>
            <input
              type="email"
              id="registerEmail"
              required
              placeholder="Email"
            />
            <span class="error-message" id="registerEmailError"></span>
          </div>
          <div class="input-group">
            <label for="registerPassword">Create password</label>
            <input
              type="password"
              id="registerPassword"
              required
              placeholder="Create password"
            />
            <span class="error-message" id="registerPasswordError"></span>
          </div>
          <div class="input-group">
            <label for="confirmPassword">Confirm password</label>
            <input
              type="password"
              id="confirmPassword"
              required
              placeholder="Confirm password"
            />
            <span class="error-message" id="confirmPasswordError"></span>
            <small class="password-hint">
              Password must have at least 6 characters, including: 1 uppercase,
              1 lowercase, 1 number
            </small>
          </div>
          <button type="submit" class="btn-primary">Signup</button>
          <p class="switch-form" style="margin: 15px 0">
            Already have an account? <a href="#" id="showLogin">Login</a>
          </p>
          <div class="divider"><span>Or</span></div>
          <button type="button" class="btn-facebook" id="facebookSignupBtn">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path
                d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"
              />
            </svg>
            Login with Facebook
          </button>
          <button type="button" class="btn-google" id="googleSignupBtn">
            <img
              src="https://developers.google.com/identity/images/g-logo.png"
              alt="Google"
              width="20"
            />
            Login with Google
          </button>
        </form>
      </div>

      <!-- Thông tin người dùng sau khi đăng nhập -->
      <div class="user-info hidden" id="userInfo">
        <h2>Chào mừng!</h2>
        <div class="user-details">
          <p><strong>Email:</strong> <span id="userEmail"></span></p>
          <p><strong>Tên:</strong> <span id="userName"></span></p>
        </div>
        <button class="btn-secondary" id="logoutBtn">Đăng xuất</button>
      </div>

      <!-- Thông báo -->
      <div class="message hidden" id="message">
        <span id="messageText"></span>
      </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>

    <!-- Custom JavaScript -->
    <script src="script.js"></script>
  </body>
</html>
