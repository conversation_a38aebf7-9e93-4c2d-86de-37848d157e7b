// Firebase Configuration - THAY ĐỔI THÔNG TIN NÀY BẰNG CONFIG CỦA DỰ ÁN FIREBASE CỦA BẠN
const firebaseConfig = {
  apiKey: "your-api-key-here",
  authDomain: "your-project-id.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project-id.appspot.com",
  messagingSenderId: "your-sender-id",
  appId: "your-app-id",
};

// Khởi tạo Firebase
firebase.initializeApp(firebaseConfig);
const auth = firebase.auth();

// DOM Elements
const loginForm = document.getElementById("loginForm");
const registerForm = document.getElementById("registerForm");
const userInfo = document.getElementById("userInfo");
const message = document.getElementById("message");

const loginFormElement = document.getElementById("loginFormElement");
const registerFormElement = document.getElementById("registerFormElement");

const showRegisterBtn = document.getElementById("showRegister");
const showLoginBtn = document.getElementById("showLogin");
const logoutBtn = document.getElementById("logoutBtn");
const googleLoginBtn = document.getElementById("googleLoginBtn");
const googleSignupBtn = document.getElementById("googleSignupBtn");
const facebookLoginBtn = document.getElementById("facebookLoginBtn");
const facebookSignupBtn = document.getElementById("facebookSignupBtn");

// Form switching - không cần thiết vì cả 2 form đều hiển thị
// Nhưng vẫn giữ để tương thích với mobile responsive
showRegisterBtn.addEventListener("click", (e) => {
  e.preventDefault();
  // Scroll to register form on mobile
  registerForm.scrollIntoView({ behavior: "smooth" });
  clearMessages();
});

showLoginBtn.addEventListener("click", (e) => {
  e.preventDefault();
  // Scroll to login form on mobile
  loginForm.scrollIntoView({ behavior: "smooth" });
  clearMessages();
});

// Email validation
function validateEmail(email) {
  return email.includes("@");
}

// Password validation
function validatePassword(password) {
  const minLength = password.length >= 6;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumber = /\d/.test(password);

  return {
    isValid: minLength && hasUpperCase && hasLowerCase && hasNumber,
    minLength,
    hasUpperCase,
    hasLowerCase,
    hasNumber,
  };
}

// Show message
function showMessage(text, type = "info") {
  const messageElement = document.getElementById("message");
  const messageText = document.getElementById("messageText");

  messageText.textContent = text;
  messageElement.className = `message ${type}`;
  messageElement.classList.remove("hidden");

  setTimeout(() => {
    messageElement.classList.add("hidden");
  }, 5000);
}

// Clear messages
function clearMessages() {
  document
    .querySelectorAll(".error-message")
    .forEach((el) => (el.textContent = ""));
  document
    .querySelectorAll("input")
    .forEach((el) => el.classList.remove("error"));
}

// Show error for specific field
function showFieldError(fieldId, message) {
  const field = document.getElementById(fieldId);
  const errorElement = document.getElementById(fieldId + "Error");

  field.classList.add("error");
  errorElement.textContent = message;
}

// Login form submission
loginFormElement.addEventListener("submit", async (e) => {
  e.preventDefault();
  clearMessages();

  const email = document.getElementById("loginEmail").value;
  const password = document.getElementById("loginPassword").value;

  // Client-side validation
  let hasError = false;

  if (!validateEmail(email)) {
    showFieldError("loginEmail", "Email phải chứa ký tự @");
    hasError = true;
  }

  if (password.length < 6) {
    showFieldError("loginPassword", "Mật khẩu phải có ít nhất 6 ký tự");
    hasError = true;
  }

  if (hasError) return;

  try {
    showMessage("Đang đăng nhập...", "info");
    await auth.signInWithEmailAndPassword(email, password);
    showMessage("Đăng nhập thành công!", "success");
  } catch (error) {
    console.error("Login error:", error);
    let errorMessage = "Đăng nhập thất bại. ";

    switch (error.code) {
      case "auth/user-not-found":
        errorMessage += "Không tìm thấy tài khoản với email này.";
        break;
      case "auth/wrong-password":
        errorMessage += "Mật khẩu không chính xác.";
        break;
      case "auth/invalid-email":
        errorMessage += "Email không hợp lệ.";
        break;
      case "auth/user-disabled":
        errorMessage += "Tài khoản đã bị vô hiệu hóa.";
        break;
      default:
        errorMessage += error.message;
    }

    showMessage(errorMessage, "error");
  }
});

// Register form submission
registerFormElement.addEventListener("submit", async (e) => {
  e.preventDefault();
  clearMessages();

  const email = document.getElementById("registerEmail").value;
  const password = document.getElementById("registerPassword").value;
  const confirmPassword = document.getElementById("confirmPassword").value;

  // Client-side validation
  let hasError = false;

  if (!validateEmail(email)) {
    showFieldError("registerEmail", "Email phải chứa ký tự @");
    hasError = true;
  }

  const passwordValidation = validatePassword(password);
  if (!passwordValidation.isValid) {
    let errorMsg = "Mật khẩu không hợp lệ: ";
    const errors = [];

    if (!passwordValidation.minLength) errors.push("ít nhất 6 ký tự");
    if (!passwordValidation.hasUpperCase) errors.push("1 chữ hoa");
    if (!passwordValidation.hasLowerCase) errors.push("1 chữ thường");
    if (!passwordValidation.hasNumber) errors.push("1 số");

    errorMsg += "cần " + errors.join(", ");
    showFieldError("registerPassword", errorMsg);
    hasError = true;
  }

  if (password !== confirmPassword) {
    showFieldError("confirmPassword", "Mật khẩu xác nhận không khớp");
    hasError = true;
  }

  if (hasError) return;

  try {
    showMessage("Đang tạo tài khoản...", "info");
    await auth.createUserWithEmailAndPassword(email, password);
    showMessage("Đăng ký thành công!", "success");
  } catch (error) {
    console.error("Register error:", error);
    let errorMessage = "Đăng ký thất bại. ";

    switch (error.code) {
      case "auth/email-already-in-use":
        errorMessage += "Email này đã được sử dụng.";
        break;
      case "auth/invalid-email":
        errorMessage += "Email không hợp lệ.";
        break;
      case "auth/weak-password":
        errorMessage += "Mật khẩu quá yếu.";
        break;
      default:
        errorMessage += error.message;
    }

    showMessage(errorMessage, "error");
  }
});

// Google login
googleLoginBtn.addEventListener("click", async () => {
  const provider = new firebase.auth.GoogleAuthProvider();

  try {
    showMessage("Signing in with Google...", "info");
    await auth.signInWithPopup(provider);
    showMessage("Google sign-in successful!", "success");
  } catch (error) {
    console.error("Google login error:", error);
    showMessage("Google sign-in failed: " + error.message, "error");
  }
});

// Google signup (same as login for OAuth)
googleSignupBtn.addEventListener("click", async () => {
  const provider = new firebase.auth.GoogleAuthProvider();

  try {
    showMessage("Signing up with Google...", "info");
    await auth.signInWithPopup(provider);
    showMessage("Google sign-up successful!", "success");
  } catch (error) {
    console.error("Google signup error:", error);
    showMessage("Google sign-up failed: " + error.message, "error");
  }
});

// Facebook login
facebookLoginBtn.addEventListener("click", async () => {
  const provider = new firebase.auth.FacebookAuthProvider();

  try {
    showMessage("Signing in with Facebook...", "info");
    await auth.signInWithPopup(provider);
    showMessage("Facebook sign-in successful!", "success");
  } catch (error) {
    console.error("Facebook login error:", error);
    showMessage("Facebook sign-in failed: " + error.message, "error");
  }
});

// Facebook signup (same as login for OAuth)
facebookSignupBtn.addEventListener("click", async () => {
  const provider = new firebase.auth.FacebookAuthProvider();

  try {
    showMessage("Signing up with Facebook...", "info");
    await auth.signInWithPopup(provider);
    showMessage("Facebook sign-up successful!", "success");
  } catch (error) {
    console.error("Facebook signup error:", error);
    showMessage("Facebook sign-up failed: " + error.message, "error");
  }
});

// Logout
logoutBtn.addEventListener("click", async () => {
  try {
    await auth.signOut();
    showMessage("Đã đăng xuất", "info");
  } catch (error) {
    console.error("Logout error:", error);
    showMessage("Lỗi khi đăng xuất: " + error.message, "error");
  }
});

// Auth state observer
auth.onAuthStateChanged((user) => {
  if (user) {
    // User is signed in - hide both forms and show user info
    loginForm.classList.add("hidden");
    registerForm.classList.add("hidden");
    userInfo.classList.remove("hidden");

    // Display user info
    document.getElementById("userEmail").textContent = user.email;
    document.getElementById("userName").textContent =
      user.displayName || "No name provided";

    console.log("User signed in:", user);
  } else {
    // User is signed out - show both forms side by side
    userInfo.classList.add("hidden");
    loginForm.classList.remove("hidden");
    registerForm.classList.remove("hidden");

    console.log("User signed out");
  }
});
