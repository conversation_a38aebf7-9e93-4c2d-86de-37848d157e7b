* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.container {
  width: 100%;
  max-width: 400px;
  position: relative;
}

.form-container {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  width: 100%;
}

.form-container h2 {
  text-align: center;
  margin-bottom: 25px;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.input-group {
  margin-bottom: 20px;
}

.input-group label {
  display: block;
  margin-bottom: 5px;
  color: #666;
  font-weight: 400;
  font-size: 14px;
}

.input-group input {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
  background: #f8f9fa;
}

.input-group input:focus {
  outline: none;
  border-color: #4f46e5;
  background: white;
}

.btn-primary,
.btn-secondary,
.btn-google {
  width: 100%;
  padding: 12px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 10px;
}

.btn-primary {
  background: #4f46e5;
  color: white;
}

.btn-primary:hover {
  background: #4338ca;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
  transform: translateY(-2px);
}

.btn-google {
  background: white;
  color: #333;
  border: 1px solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.btn-google:hover {
  background: #f8f9fa;
  border-color: #4f46e5;
}

.switch-form {
  text-align: center;
  margin-top: 20px;
  color: #666;
}

.switch-form a {
  color: #4f46e5;
  text-decoration: none;
  font-weight: 600;
}

.switch-form a:hover {
  text-decoration: underline;
}

.hidden {
  display: none;
}

.user-info {
  background: white;
  padding: 40px;
  border-radius: 10px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.user-info h2 {
  color: #333;
  margin-bottom: 20px;
}

.user-details {
  margin-bottom: 30px;
  text-align: left;
}

.user-details p {
  margin-bottom: 10px;
  font-size: 16px;
  color: #555;
}

.message {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px 20px;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  z-index: 1000;
  transition: all 0.3s ease;
}

.message.success {
  background: #27ae60;
}

.message.info {
  background: #3498db;
}

.divider {
  text-align: center;
  margin: 15px 0;
  color: #999;
  font-size: 14px;
  position: relative;
}

.divider::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #ddd;
  z-index: 1;
}

.divider span {
  background: white;
  padding: 0 15px;
  position: relative;
  z-index: 2;
}

.password-hint {
  color: #666;
  font-size: 12px;
  margin-top: 5px;
  display: block;
}

@media (max-width: 480px) {
  .container {
    padding: 10px;
  }

  .form-container {
    padding: 25px 20px;
  }
}
